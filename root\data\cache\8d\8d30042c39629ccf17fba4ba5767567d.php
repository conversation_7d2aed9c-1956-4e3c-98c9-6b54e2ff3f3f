<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* mod_app_index.html.twig */
class __TwigTemplate_1d57556c11773f99308f1522aaefaa90 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'meta_title' => [$this, 'block_meta_title'],
            'styles' => [$this, 'block_styles'],
            'body_class' => [$this, 'block_body_class'],
            'breadcrumb' => [$this, 'block_breadcrumb'],
            'page_header' => [$this, 'block_page_header'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return $this->loadTemplate(((CoreExtension::getAttribute($this->env, $this->source, ($context["request"] ?? null), "ajax", [], "any", false, false, false, 1)) ? ("layout_blank.html.twig") : ("layout_default.html.twig")), "mod_app_index.html.twig", 1);
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        yield from $this->getParent($context)->unwrap()->yield($context, array_merge($this->blocks, $blocks));
    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(__trans("App module"), "html", null, true);
        yield from [];
    }

    // line 4
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_styles(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        yield from [];
    }

    // line 6
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body_class(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        yield "example";
        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumb(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        yield " <li class=\"active\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(__trans("App module"), "html", null, true);
        yield "</li>";
        yield from [];
    }

    // line 9
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_header(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 10
        yield "<article class=\"page-header\">
    <h1>";
        // line 11
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(__trans("App module title"), "html", null, true);
        yield "</h1>
</article>
";
        yield from [];
    }

    // line 14
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 15
        yield "<link rel=\"stylesheet\" href=\"/modules/App/Assets/css/appstyles.css\">
<link rel=\"stylesheet\" href=\"/modules/App/Assets/css/ai-assistant-styles.css\">
<!-- Main Container -->
<div class=\"container-fluid g-0 d-flex flex-column\">
  <!-- Main Content -->
  <main class=\"flex-grow-1 d-flex flex-column\">
    <!-- Tab Navigation (Visible in default layout) -->
    <nav class=\"border-bottom tab-navigation\">
      <div class=\"container-fluid\">
        <ul class=\"nav nav-tabs border-0\" id=\"mainTabs\" role=\"tablist\">
          <li class=\"nav-item\" role=\"presentation\">
            <button class=\"nav-link active\" id=\"setup-tab\" data-bs-toggle=\"tab\" data-bs-target=\"#setup\" type=\"button\" role=\"tab\" aria-controls=\"setup\" aria-selected=\"true\">
            <i class=\"ti ti-info-square-rounded fs-4 me-2\"></i></i> Details
            </button>
          </li>
          <li class=\"nav-item\" role=\"presentation\">
            <button class=\"nav-link\" id=\"agenda-tab\" data-bs-toggle=\"tab\" data-bs-target=\"#agenda\" type=\"button\" role=\"tab\" aria-controls=\"agenda\" aria-selected=\"false\">
            <i class=\"ti ti-clipboard-list fs-4 me-2\"></i> Agenda
            </button>
          </li>
          <li class=\"nav-item\" role=\"presentation\">
            <button class=\"nav-link\" id=\"actions-tab\" data-bs-toggle=\"tab\" data-bs-target=\"#actions\" type=\"button\" role=\"tab\" aria-controls=\"actions\" aria-selected=\"false\">
            <i class=\"ti ti-subtask fs-4 me-2\"></i></i> Actions
            </button>
          </li>
        </ul>
      </div>
    </nav>
    <!-- Layout Container -->
    <div class=\"layout-container flex-grow-1\">
      <!-- Default Layout (Tabbed) -->
      <div class=\"layout-default h-100\">
        <div class=\"tab-content h-100\" id=\"mainTabsContent\">
          <!-- Details Tab -->
          <div class=\"tab-pane fade show active h-100\" id=\"setup\" role=\"tabpanel\" aria-labelledby=\"setup-tab\">
            <div class=\"container-fluid h-100 pt-3\">
              <div id=\"meenoe-details-tab\" class=\"row h-100 justify-content-center\">
                <div class=\"col-12 col-xl-10 mx-auto d-flex flex-column gap-4\">
                  <section class=\"pt-5 pt-md-14 pt-lg-12 pb-4 pb-md-5 pb-lg-14\">
                    <div class=\"container-fluid\">
                      <div class=\"d-flex mb-4 gap-3\">
                        <h2 id=\"meenoe-name\" class=\"fs-10 fw-bolder text-center mb-0\">Name Your Meenoe Here</h2>
                        <a id=\"edit-meenoe-name\" class=\"btn btn-gradient\">
                        <i class=\"ti ti-edit me-2\"></i>
                        Edit Meenoe Title
                        </a>
                      </div>
                      <div class=\"row\">
                        <div class=\"col-lg-12\">
              <div class=\"d-flex mb-4 gap-3\">
                            <h5 class=\"fs-10 fw-bolder\">Objective</h5>
              <a id=\"edit-objective\" type=\"button\" class=\"\">
                              <i class=\"ti ti-edit me-2\"></i>
              </a>
              </div>
                          <p id=\"objective-text\" class=\"fs-4 border-bottom pb-3 mb-5\">
                            Enter your Meenoe objective or an introduction here 
                          </p>
                        </div>
                      </div>
                      <div id=\"meenoe-at-a-glance\" class=\"row\">
                        <div class=\"col-lg-3 col-md-6 d-flex align-items-stretch\">
                          <div class=\"card w-100 bg-primary-subtle rounded-24 meenoe-shadow physicsCard\" style=\"position:relative;\">
                            <div class=\"card-body text-center px-8 py-5\" style=\"padding-top:35px !important;\">
                              <h5 class=\"my-3 fw-bolder fs-5\">
                                <i class=\"ti ti-users fs-4 me-2\"></i>
                                <span id=\"users-count\">0</span> Users
                              </h5>
                              <p class=\"mb-0 fs-4\">People participating in this meenoe</p>
                            </div>
                          </div>
                        </div>
                        <div class=\"col-lg-3 col-md-6 d-flex align-items-stretch\">
                          <div class=\"card w-100 bg-info-subtle rounded-24 meenoe-shadow physicsCard\" style=\"position:relative;\">
                            <div class=\"card-body text-center px-9 py-10\" style=\"padding-top:35px !important;\">
                              <h5 class=\"mb-3 fw-bolder fs-5\">
                                <i class=\"ti ti-clipboard-list fs-4 me-2\"></i>
                                <span id=\"agenda-count\">0</span> Agenda Points
                              </h5>
                              <p class=\"mb-0 fs-4\">Topics to be discussed</p>
                            </div>
                          </div>
                        </div>
                        <div class=\"col-lg-3 col-md-6 d-flex align-items-stretch\">
                          <div class=\"card meenoe-shadow w-100 bg-success-subtle rounded-24 physicsCard\" style=\"position:relative;\">
                            <div class=\"card-body text-center px-8 py-5\" style=\"padding-top:35px !important;\">
                              <h5 class=\"my-3 fw-bolder fs-5\">
                                <i class=\"ti ti-paperclip fs-4 me-2\"></i>
                                <span id=\"files-count\">0</span> Files
                              </h5>
                              <p class=\"mb-0 fs-4\">Documents and attachments</p>
                            </div>
                          </div>
                        </div>
                        <div class=\"col-lg-3 col-md-6 d-flex align-items-stretch\">
                          <div class=\"card meenoe-shadow w-100 bg-warning-subtle rounded-24 physicsCard\" style=\"position:relative;\">
                            <div class=\"card-body text-center px-8 py-5\" style=\"padding-top:35px !important;\">
                              <h5 class=\"my-3 fw-bolder fs-5\">
                                <i class=\"ti ti-subtask fs-4 me-2\"></i>
                                <span id=\"actions-count\">0</span> Actions
                              </h5>
                              <p class=\"mb-0 fs-4\">Tasks and follow-ups</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </section>
                </div>
              </div>
            </div>
          </div>
          <!-- agenda Tab -->
          <div class=\"tab-pane fade h-100\" id=\"agenda\" role=\"tabpanel\" aria-labelledby=\"agenda-tab\">
            <div class=\"container-fluid h-100 pt-1\">
              <div class=\"row h-100\">
                <div class=\"col-12\">
                  <div class=\"row\">
                    <div class=\"col-lg-4\">
                      <div class=\"row mb-4\">
                        <div class=\"col-lg-8\">
                          <form class=\"position-relative\">
                            <input type=\"text\" class=\"form-control border-primary search-agenda-point py-2 ps-5\" id=\"search-agenda-point\" placeholder=\"Search Agenda Points\">
                            <i class=\"ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3\"></i>
                          </form>
                        </div>
                        <div class=\"col-lg-4\">
                          <a href=\"javascript:void(0)\" class=\"btn btn-gradient d-flex align-items-center justify-content-center h-100 w-100 py-2\" id=\"add-agenda-point\">
                          <i class=\"ti ti-list-details fs-4 me-2\"></i>
                          <span class=\"d-none d-md-block fw-medium fs-3\">Add</span>
                          </a>
                        </div>
                      </div>
                      <!-- agenda cards go here -->
                      <div class=\"all-agenda-points-outer\">
                        <div id=\"all-agenda-points\">
                        </div>
                      </div>
                    </div>
                    <!-- first half end -->
                    <div id=\"meenoe-agenda-details\" class=\"col-lg-8\">
                      <div class=\"position-relative overflow-hidden\">
                        <div class=\"position-relative\">
                          <div id=\"agenda-point-details\" class=\"mh-n100 p-9\" data-simplebar=\"init\">
                            <div class=\"threads-list threads w-100\" data-user-id=\"8\">
                              <div class=\"hstack align-items-start mb-4 pb-1 align-items-center justify-content-between flex-wrap gap-6\">
                                <div class=\"d-flex align-items-center gap-2\">
                                  <div>
                                    <h4 id=\"agenda-point-title\" class=\"agenda-point-title fw-semibold mb-2\" style=\"cursor: text;\">
                                      Provide an update on sales team
                                    </h4>
                                  </div>
                                </div>
                                <div class=\"setPointUrgency d-flex gap-2\">
                                  <span id=\"details-urgency-pill\" class=\"badge text-bg-primary\">
                                  Important
                                  </span>
                                  <div class=\"dropdown disoc\">
                                    <a class=\"fs-6 nav-icon-hover color-inherit\" href=\"javascript:void(0)\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">
                                    <i class=\"ti ti-dots-vertical\"></i>
                                    </a>
                                    <ul class=\"dropdown-menu\" style=\"\">
                                      <li>
                                        <a class=\"dropdown-item d-flex align-items-center gap-2 border-bottom normal-urgency urgency-option\" href=\"#\" data-urgency=\"normal\"><span><i class=\"ti ti-point-filled fs-5\"></i>
                                        </span>Normal
                                        </a>
                                      </li>
                                      <li>
                                        <a class=\"dropdown-item d-flex align-items-center gap-2 border-bottom moderate-urgency urgency-option\" href=\"#\" data-urgency=\"moderate\"><span><i class=\"ti ti-point-filled fs-5 text-secondary\"></i></span>Moderate</a>
                                      </li>
                                      <li>
                                        <a class=\"dropdown-item d-flex align-items-center gap-2 border-bottom important-urgency urgency-option\" href=\"#\" data-urgency=\"important\"><span><i class=\"ti ti-point-filled fs-5 text-primary\"></i></span>Important</a>
                                      </li>
                                      <li>
                                        <a class=\"dropdown-item d-flex align-items-center gap-2 border-bottom critical-urgency urgency-option\" href=\"#\" data-urgency=\"critical\"><span><i class=\"ti ti-point-filled fs-5 text-warning\"></i></span>Critical</a>
                                      </li>
                                      <li>
                                        <a class=\"dropdown-item d-flex align-items-center gap-2 border-bottom mandatory-urgency urgency-option\" href=\"#\" data-urgency=\"mandatory\"><span><i class=\"ti ti-point-filled fs-5 text-danger\"></i></span>Mandatory</a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                              <div class=\"meenoethreadsouter\">
                                <div class=\"meenoethreads w-100\">
                                  <div class=\"hstack gap-3 align-items-start mb-7 justify-content-start agenda-thread-post\">
                                    <img src=\"https://bootstrapdemos.adminmart.com/modernize/dist/assets/images/profile/user-8.jpg\" alt=\"user8\" width=\"40\" height=\"40\" class=\"rounded-circle comment-user-prof-img\">
                                    <div class=\"\">
                                      <h6 class=\"comment-username fs-2 text-muted\">
                                        Andrew Tate
                                        <span class=\"comment-date-time\">10:00 am, 2/22/2024</span>
                                      </h6>
                                      <div class=\"thread-text p-2 text-bg-thread rounded-1 d-inline-block text-dark fs-3\">
                                        We achieved about 20% increases in our last week's performance by changing one line in the sales script. would love to share it with the team!
                                      </div>
                                      <div class=\"thread-actions d-flex align-items-center\">
                                        <div class=\"d-flex align-items-center gap-2\">
                                          <a class=\"edit-thread p-0 hstack justify-content-center\" href=\"javascript:void(0)\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-title=\"edit\">
                                          <i class=\"ti ti-edit fs-4 text-primary\"></i>
                                          </a>
                                        </div>
                                        <div class=\"d-flex align-items-center gap-2 ms-4\">
                                          <a class=\"delete-thread p-0 hstack justify-content-center\" href=\"javascript:void(0)\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-title=\"Approve\">
                                          <i class=\"ti ti-star text-muted fs-4\"></i>
                                          </a>
                                        </div>
                                        <div class=\"d-flex align-items-center gap-2 ms-4\">
                                          <a class=\"delete-thread p-0 hstack justify-content-center\" href=\"javascript:void(0)\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-title=\"Delete\">
                                          <i class=\"ti ti-trash text-danger fs-4\"></i>
                                          </a>
                                        </div>
                                        <a class=\"connect-action text-dark ms-auto d-flex align-items-center justify-content-center bg-transparent p-2 fs-4 rounded-circle\" href=\"javascript:void(0)\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-title=\"Create an action\">
                                        <i class=\"ti ti-steam\"></i>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div class=\"hstack gap-3 align-items-start mb-7 justify-content-start agenda-thread-post\">
                                    <img src=\"https://bootstrapdemos.adminmart.com/modernize/dist/assets/images/profile/user-4.jpg\" alt=\"user8\" width=\"40\" height=\"40\" class=\"rounded-circle\">
                                    <div class=\"\">
                                      <h6 class=\"fs-2 text-muted\">
                                        Sally, 1.8 hours ago
                                      </h6>
                                      <div class=\"p-2 text-bg-light rounded-1 d-inline-block text-dark fs-3\">
                                        Amazing Andrew! we also have some pretty good results from our strats, though not as amazing as yours. can't wait to see it, wanna share your implementation file?
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class=\"agendafiles\">
                                <div class=\"mb-3\">
                                  <div class=\"d-flex align-items-center justify-content-between\">
                                    <ul class=\"list-unstyled mb-0 d-flex align-items-center gap-7\">
                                      <li>
                                        <a id=\"agenda-add-thread\" class=\"btn btn-light align-items-center meenoe-shadow d-flex gap-1\" href=\"javascript:void(0)\">
                                        <i class=\"ti ti-message-plus\"></i>
                                        Add Thread
                                        </a>
                                      </li>
                                      <li>
                                        <a id=\"agenda-audio-clip\" class=\"btn btn-light align-items-center meenoe-shadow d-flex gap-1\" href=\"javascript:void(0)\">
                                        <i class=\"ti ti-microphone\"></i>
                                        Add Voice Clip
                                        </a>
                                      </li>
                                      <li>
                                        <a id=\"agenda-file-upload\" class=\"btn btn-light align-items-center meenoe-shadow d-flex gap-1\" href=\"javascript:void(0)\">
                                        <i class=\"ti ti-file-upload\"></i>
                                        Upload File
                                        </a>
                                      </li>
                                      <li>
                                        <a id=\"agenda-view-files\" class=\"btn btn-light align-items-center meenoe-shadow d-flex gap-1\"  data-bs-toggle=\"offcanvas\" data-bs-target=\"#filesOffcanvas\" aria-controls=\"filesOffcanvas\">
                                        <i class=\"ti ti-paperclip\"></i>
                                        View Files
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <!-- Files section is now hidden - files are shown in off-canvas -->
                                <div id=\"agenda-files-list\" class=\"d-none\">
                                  <!-- This div is now hidden as files are displayed in the off-canvas -->
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Actions Tab -->
          <div class=\"tab-pane transparentbg fade h-100\" id=\"actions\" role=\"tabpanel\" aria-labelledby=\"actions-tab\">
            <!-- actions -->
            <div id=\"meenoe-actions-section\" class=\"p-3 mb-2\">
              <ul class=\"nav nav-pills p-2 rounded align-items-center flex-row\">
                <div class=\"card-actions cursor-pointer ms-auto d-flex button-group gap-3 disoc\">
                  <a class=\"btn btn-gradient d-flex align-items-center px-3 gap-6\" id=\"meenoe-action-template disoc\">
                  <i class=\"ti ti-template fs-4\"></i>
                  <span class=\"d-none d-md-block font-weight-medium fs-3\">Use Action Template</span>
                  </a>
                  <a class=\"btn btn-gradient d-flex align-items-center px-3 gap-6 disoc\" id=\"meenoe-add-action\">
                  <i class=\"ti ti-subtask fs-4\"></i>
                  <span class=\"d-none d-md-block font-weight-medium fs-3\">Add Action</span>
                  </a>
                  <!-- actions drop down -->
                  <div class=\"dropdown d-inline-flex align-items-center justify-content-center disoc\">
                    <a class=\"color-inherit fs-6 nav-icon-hover\" href=\"javascript:void(0)\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">
                    <i class=\"ti ti-dots-vertical\"></i>
                    </a>
                    <ul class=\"dropdown-menu\" style=\"\">
                      <li>
                        <a class=\"dropdown-item d-flex align-items-center gap-2 border-bottom\" href=\"javascript:void(0)\" onclick=\"expand_all()\">
                        <span>
                        <i class=\"ti ti-circle-plus fs-4\"></i>
                        </span>
                        Expand Branches
                        </a>
                      </li>
                      <li>
                        <a class=\"dropdown-item d-flex align-items-center gap-2 border-bottom\" href=\"javascript:void(0)\" onclick=\"collapse_all()\">
                        <span>
                        <i class=\"ti ti-circle-minus fs-4\"></i>
                        </span>
                        Collapse Branches
                        </a>
                      </li>
                      <li>
                        <a class=\"dropdown-item d-flex align-items-center gap-2\" href=\"javascript:void(0)\" data-action=\"collapse-all\">
                        <i class=\"ti ti-minus fs-4\"></i>
                        <span>
                        Close all cards
                        </span>
                        </a>
                      </li>
                    </ul>
                  </div>
                  <!-- actions drop down end -->
                </div>
              </ul>
              <!-- empty agenda list end -->
            </div>
            <div id=\"meenoe-actions\">
              <!--  Row 2 action flow -->
              <div id=\"div_tree\" class=\"w-100 mx-auto\">
                <!-- empty agenda list graphic here -->
                <div id=\"zero-actions\" class=\"card shadow-none\">
                  <div class=\"card-body text-center\">
                    <h6 class=\"fs-3 mb-3\">There are no actions in this Meenoe, let's add some!</h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Split Layout (for running page and other layouts) -->
      <div class=\"layout-split d-none\">
        <div class=\"container-fluid h-100 p-0\">
          <div class=\"h-100 overflow-auto\">
            <div id=\"running-details\" class=\"mb-5\">
              <!-- Details content will be moved here -->
            </div>
            <div id=\"running-flow\" class=\"mb-5\">
              <!-- Flow content will be moved here -->
            </div>
            <div id=\"running-actions\">
              <!-- Actions content will be moved here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
<!-- Add User Modal -->
<div class=\"modal fade\" id=\"addUserModal\" tabindex=\"-1\" aria-labelledby=\"addUserModalLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog modal-xl modal-dialog-scrollable\">
    <div class=\"modal-content\">
      <div class=\"modal-header flex-column align-items-start border-0 pb-0\">
        <div class=\"d-flex justify-content-between w-100 align-items-center mb-3\">
          <h5 class=\"modal-title\" id=\"addUserModalLabel\">People</h5>
          <div class=\"d-flex align-items-center\">
            <div class=\"input-group input-group-sm me-3\" style=\"width: 250px;\">
              <input type=\"text\" class=\"form-control\" id=\"userSearchInput\" placeholder=\"Search by name\">
              <span class=\"input-group-text\"><i class=\"bi bi-search\"></i></span>
            </div>
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
          </div>
        </div>
        <!-- Controls Toolbar for Modal -->
        <div class=\"modal-controls-toolbar d-flex justify-content-between align-items-center w-100 pt-2 pb-3 mb-3\">
          <div>
            <button class=\"btn btn-sm btn-outline-primary me-2\">Design Team <i class=\"bi bi-chevron-down ms-1\"></i></button>
            <button class=\"btn btn-sm btn-outline-secondary me-2\" id=\"modalSortOrderButton\">Position <i class=\"bi bi-arrow-down-up ms-1\"></i></button>
          </div>
          <div class=\"d-flex align-items-center\">
            <span class=\"text-muted me-2 small\">Sort by:</span>
            <select class=\"form-select form-select-sm me-2\" style=\"width: auto;\" id=\"modalSortBySelect\">
              <option value=\"name\" selected>Name</option>
              <option value=\"progress\">Progress</option>
            </select>
            <div class=\"btn-group btn-group-sm\" role=\"group\" id=\"modalLayoutToggle\">
              <button type=\"button\" class=\"btn btn-outline-secondary active\"><i class=\"bi bi-grid-3x3-gap-fill\"></i></button>
              <button type=\"button\" class=\"btn btn-outline-secondary\"><i class=\"bi bi-list-ul\"></i></button>
            </div>
          </div>
        </div>
      </div>
      <div class=\"modal-body\">
        <div class=\"row\" id=\"modalUserList\">
          <!-- User cards will be populated here by JavaScript -->
        </div>
      </div>
      <div class=\"modal-footer justify-content-between flex-wrap\">
        <div class=\"w-100 mb-2\">
          <div id=\"guestPillContainer\" class=\"guest-pill-container mb-2\">
            <!-- Guest pills will be added here by JavaScript -->
          </div>
          <div class=\"input-group\">
            <input type=\"email\" class=\"form-control form-control-sm\" id=\"guestEmailInput\" placeholder=\"Invite guest by email\">
            <button class=\"btn btn-sm btn-outline-secondary\" type=\"button\" id=\"inviteGuestButton\">Invite</button>
            <select class=\"form-select form-select-sm\" id=\"guestPermissionSelect\" style=\"max-width: 120px;\">
              <option value=\"viewer\" selected>Viewer</option>
              <option value=\"contributor\">Contributor</option>
            </select>
          </div>
        </div>
        <button type=\"button\" class=\"btn btn-primary\" id=\"addSelectedUsersButton\">Add people</button>
      </div>
    </div>
  </div>
</div>
<!-- Action Users Modal -->
<div class=\"modal fade\" id=\"actionUsersModal\" tabindex=\"-1\" aria-labelledby=\"actionUsersModalLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog modal-lg modal-dialog-scrollable\">
    <div class=\"modal-content\">
      <div class=\"modal-header flex-column align-items-start border-0 pb-0\">
        <div class=\"d-flex justify-content-between w-100 align-items-center mb-3\">
          <h5 class=\"modal-title\" id=\"actionUsersModalLabel\">Manage Action Users</h5>
          <div class=\"d-flex align-items-center\">
            <div class=\"input-group input-group-sm me-3\" style=\"width: 250px;\">
              <input type=\"text\" class=\"form-control\" id=\"actionUserSearchInput\" placeholder=\"Search by name\">
              <span class=\"input-group-text\"><i class=\"bi bi-search\"></i></span>
            </div>
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
          </div>
        </div>
        <div class=\"w-100\">
          <p class=\"text-muted mb-0\">Select users who will be responsible for this action and set their permissions</p>
        </div>
      </div>
      <div class=\"modal-body\">
        <div id=\"actionUserList\" class=\"row\">
          <!-- User cards will be populated here by JavaScript -->
        </div>
        <div id=\"noUsersMessage\" class=\"text-center text-muted py-4 d-none\">
          <i class=\"ti ti-users-off fs-1\"></i>
          <h6 class=\"mt-2\">No Users Available</h6>
          <p class=\"small\">Add users to this meeting first to assign them to actions.</p>
          <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" data-bs-toggle=\"modal\" data-bs-target=\"#addUserModal\">
          Add Users to Meeting
          </button>
        </div>
      </div>
      <div class=\"modal-footer justify-content-between\">
        <div class=\"d-flex align-items-center\">
          <small class=\"text-muted\">
          <i class=\"ti ti-info-circle me-1\"></i>
          Permissions control what users can do with this action
          </small>
        </div>
        <div>
          <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
          <button type=\"button\" class=\"btn btn-primary\" id=\"saveActionUsersButton\">Save Users</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Link Agenda Modal -->
<div class=\"modal fade\" id=\"L-A-modal\" tabindex=\"-1\" aria-labelledby=\"linkAgendaModalLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog modal-lg modal-dialog-scrollable\">
    <div class=\"modal-content\">
      <div class=\"modal-header\">
        <h5 class=\"modal-title\" id=\"linkAgendaModalLabel\">Link Agenda Point</h5>
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
      </div>
      <div class=\"modal-body\" id=\"linked-agenda-modalBody\">
        <div class=\"mb-3\">
          <p class=\"text-muted\">Select an agenda point to link with this action:</p>
        </div>
        <div class=\"agenda-search-container mb-3\">
          <div class=\"input-group\">
            <span class=\"input-group-text\"><i class=\"ti ti-search\"></i></span>
            <input type=\"text\" class=\"form-control border-primary\" id=\"agendaSearchInput\" placeholder=\"Search agenda points...\">
          </div>
        </div>
        <ul class=\"list-group\" id=\"linked-agenda-list\">
          <!-- Agenda points will be populated here by JavaScript -->
        </ul>
        <div id=\"noAgendaMessage\" class=\"text-center text-muted py-4 d-none\">
          <i class=\"ti ti-clipboard-off fs-1\"></i>
          <h6 class=\"mt-2\">No Agenda Points Available</h6>
          <p class=\"small\">Create some agenda points first to link them to actions.</p>
        </div>
      </div>
      <div class=\"modal-footer\">
        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
        <button type=\"button\" class=\"btn btn-primary\" id=\"setButton\" disabled>Link Selected</button>
      </div>
    </div>
  </div>
</div>
<!-- Files Off-canvas -->
<div class=\"offcanvas offcanvas-end\" tabindex=\"-1\" id=\"filesOffcanvas\" aria-labelledby=\"filesOffcanvasLabel\">
  <div class=\"offcanvas-header\">
    <h5 class=\"offcanvas-title\" id=\"filesOffcanvasLabel\">
      <i class=\"ti ti-paperclip me-2\"></i>Files
    </h5>
    <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"offcanvas\" aria-label=\"Close\"></button>
  </div>
  <div class=\"offcanvas-body\">
    <div id=\"files-chat-container\" class=\"files-chat\">
      <!-- Files will be populated here by JavaScript -->
      <div class=\"text-center py-5\" id=\"no-files-message\">
        <i class=\"ti ti-file-off fs-1 text-muted\"></i>
        <p class=\"mt-2 text-muted\">No files uploaded yet.</p>
      </div>
    </div>
  </div>
</div>
<!-- Floating AI Assistant Button -->
<button id=\"aiAssistantButton\" class=\"ai-assistant-btn\" data-bs-toggle=\"offcanvas\" data-bs-target=\"#aichatoffcanvas\" aria-controls=\"aichatoffcanvas\">
  <i class=\"ti ti-sparkles fs-5\"></i>
</button>

  <!-- ai chat off canvas -->
<div class=\"offcanvas offcanvas-end\" tabindex=\"-1\" id=\"aichatoffcanvas\" data-bs-scroll=\"true\" aria-labelledby=\"aichatoffcanvasLabel\">
  <!-- Floating close button -->
  <button type=\"button\" class=\"btn-close floating-close\" data-bs-dismiss=\"offcanvas\" aria-label=\"Close\">
    <i class=\"ti ti-x\"></i>
  </button>

  <div id=\"app\">
    <div class=\"chat-container\">
<div class=\"chat-header\">
        <div class=\"header-right d-flex align-items-center\">
          <button id=\"clear-chat\" class=\"icon-button\">
            <i class=\"ti ti-message-plus\"></i>
            New Chat
          </button>
          <button id=\"ai-settings-btn\" class=\"icon-button ms-2\" type=\"button\" title=\"AI Settings\" data-bs-toggle=\"tooltip\" data-bs-title=\"AI Settings\">
            <i class=\"ti ti-settings\"></i>
          </button>
        </div>
      </div>
      <div class=\"chat-content\">
        <div class=\"chat-main\">
          <div id=\"chat-messages\"></div>
          <div id=\"chat-input\">
            <div id=\"knowledge-base-toggle\" class=\"dropup position-absolute\">
              <button class=\"btn btn-dark rounded-circle\" type=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">
              <i class=\"ti ti-paperclip\"></i>
              </button>
              <ul class=\"dropdown-menu\">
                <div class=\"files-sidebar\">
                  <div class=\"files-header\">
                    <h3>Knowledge Base</h3>
                    <div class=\"file-search\">
                      <input type=\"text\" id=\"file-search\" placeholder=\"Search files...\">
                    </div>
                  </div>
                  <div id=\"files-list\"></div>
                </div>
              </ul>
            </div>
            <div class=\"chat-input-wrapper\">
              <button id=\"upload-file\" class=\"icon-button\">
              <i class=\"ti ti-plus\"></i>
              </button>
              <textarea id=\"user-input\" rows=\"1\" placeholder=\"Type your message...\"></textarea>
              <button id=\"micButton\">
              <i class=\"ti ti-microphone\"></i>
              </button>
              <button id=\"send-button\" class=\"icon-button\">
              <i class=\"ti ti-arrow-up\"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Meenoe Details Styles -->
<style>
/* Editing states for meenoe details */
.editing {
    background-color: #fff3cd !important;
    border: 2px dashed #ffc107 !important;
    border-radius: 8px !important;
    padding: 8px !important;
    outline: none !important;
    transition: all 0.3s ease;
}

.editing:focus {
    background-color: #fff !important;
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

/* Counter animation */
#users-count, #agenda-count, #files-count, #actions-count {
    transition: transform 0.2s ease;
    display: inline-block;
}

/* At-a-glance card hover effects */
#meenoe-at-a-glance .card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

#meenoe-at-a-glance .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

/* Name syncing visual feedback */
#meenoe-name2 input {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#meenoe-name2 input:focus {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

.name-syncing {
    animation: syncPulse 0.5s ease-in-out;
}

@keyframes syncPulse {
    0% { background-color: transparent; }
    50% { background-color: rgba(13, 110, 253, 0.1); }
    100% { background-color: transparent; }
}

/* Edit button states */
#edit-meenoe-name.btn-success,
#edit-objective.text-success {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
</style>
<!-- Quill.js -->
<script src=\"https://cdnjs.cloudflare.com/ajax/libs/quill/2.0.2/quill.min.js\"></script>
<!-- Flatpickr -->
<script src=\"https://cdnjs.cloudflare.com/ajax/libs/flatpickr/4.6.13/flatpickr.min.js\"></script>
    
<script src=\"/modules/App/assets/js/bootstrap-init.js\"></script>
<script src=\"/modules/App/assets/js/Sortable.min.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/action-users.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/agendaflow.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/ai-chat-integration.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/ai-system-loader.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/at-a-glance-cards.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/datepicker.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/edgeTTS.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/file-drop.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/layout-manager.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/meenoe-details.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/meenoe-integrations.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/meenoe-state.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/meenoeInit.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/meenoeactions.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/people-modal.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/publishing-status.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/utils.js\" defer=\"defer\"></script>
<script src=\"/modules/App/assets/js/waveform-audio-player.js\" defer=\"defer\"></script>
";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "mod_app_index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  125 => 15,  118 => 14,  110 => 11,  107 => 10,  100 => 9,  87 => 7,  76 => 6,  66 => 4,  55 => 3,  45 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "mod_app_index.html.twig", "C:\\Users\\<USER>\\Desktop\\uswwebserver\\USBWebServer\\root\\modules\\App\\html_client/mod_app_index.html.twig");
    }
}
